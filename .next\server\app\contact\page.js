/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/contact/page";
exports.ids = ["app/contact/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'contact',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/contact/page.tsx */ \"(rsc)/./src/app/contact/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/contact/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/contact/page\",\n        pathname: \"/contact\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Barlow%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22display%22%3A%22swap%22%2C%22preload%22%3Atrue%2C%22variable%22%3A%22--font-barlow%22%7D%5D%2C%22variableName%22%3A%22barlow%22%7D&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Ccomponents%5Cperformance%5CPerformanceMonitor.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Barlow%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22display%22%3A%22swap%22%2C%22preload%22%3Atrue%2C%22variable%22%3A%22--font-barlow%22%7D%5D%2C%22variableName%22%3A%22barlow%22%7D&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Ccomponents%5Cperformance%5CPerformanceMonitor.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/performance/PerformanceMonitor.tsx */ \"(ssr)/./src/components/performance/PerformanceMonitor.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDU2F1ZCU1Q1Byb2plY3RzJTVDbmF2aGF1cyU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIyQmFybG93JTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTJDJTIyd2VpZ2h0JTIyJTNBJTVCJTIyMzAwJTIyJTJDJTIyNDAwJTIyJTJDJTIyNTAwJTIyJTJDJTIyNjAwJTIyJTJDJTIyNzAwJTIyJTJDJTIyODAwJTIyJTJDJTIyOTAwJTIyJTVEJTJDJTIyZGlzcGxheSUyMiUzQSUyMnN3YXAlMjIlMkMlMjJwcmVsb2FkJTIyJTNBdHJ1ZSUyQyUyMnZhcmlhYmxlJTIyJTNBJTIyLS1mb250LWJhcmxvdyUyMiU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmJhcmxvdyUyMiU3RCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q1NhdWQlNUNQcm9qZWN0cyU1Q25hdmhhdXMlNUNzcmMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q1NhdWQlNUNQcm9qZWN0cyU1Q25hdmhhdXMlNUNzcmMlNUNjb21wb25lbnRzJTVDcGVyZm9ybWFuY2UlNUNQZXJmb3JtYW5jZU1vbml0b3IudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL25hdmhhdXMtd2Vic2l0ZS8/ZTNhYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFNhdWRcXFxcUHJvamVjdHNcXFxcbmF2aGF1c1xcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxwZXJmb3JtYW5jZVxcXFxQZXJmb3JtYW5jZU1vbml0b3IudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Barlow%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22display%22%3A%22swap%22%2C%22preload%22%3Atrue%2C%22variable%22%3A%22--font-barlow%22%7D%5D%2C%22variableName%22%3A%22barlow%22%7D&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Ccomponents%5Cperformance%5CPerformanceMonitor.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp%5Ccontact%5Cpage.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp%5Ccontact%5Cpage.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/contact/page.tsx */ \"(ssr)/./src/app/contact/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDU2F1ZCU1Q1Byb2plY3RzJTVDbmF2aGF1cyU1Q3NyYyU1Q2FwcCU1Q2NvbnRhY3QlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uYXZoYXVzLXdlYnNpdGUvP2Y3ZjEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxTYXVkXFxcXFByb2plY3RzXFxcXG5hdmhhdXNcXFxcc3JjXFxcXGFwcFxcXFxjb250YWN0XFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp%5Ccontact%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/contact/page.tsx":
/*!**********************************!*\
  !*** ./src/app/contact/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Contact)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_PageWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/PageWrapper */ \"(ssr)/./src/components/layout/PageWrapper.tsx\");\n/* harmony import */ var _components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/shapes/AnimatedShapes */ \"(ssr)/./src/components/shapes/AnimatedShapes.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Contact() {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        message: \"\"\n    });\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        // Handle form submission here\n        console.log(\"Form submitted:\", formData);\n        // Reset form\n        setFormData({\n            name: \"\",\n            email: \"\",\n            message: \"\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_PageWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-5xl md:text-6xl font-bold leading-none\",\n                                                children: \"Have something worth building?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl leading-relaxed text-gray-700 max-w-lg\",\n                                                children: \"We like projects that are sharp, fast, and meaningful. If that's what you've got, we should talk.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-bauhaus-blue text-bauhaus-white p-6 rounded-3xl relative overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute bottom-2 right-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                                            size: \"sm\",\n                                                            color: \"yellow\",\n                                                            className: \"w-4 h-4\",\n                                                            animationPreset: \"gentle\",\n                                                            animationIndex: 1\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 57,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 56,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative z-10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-bold mb-2\",\n                                                                children: \"Email us directly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 60,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg\",\n                                                                children: \"<EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 61,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 59,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-bauhaus-yellow text-bauhaus-black p-6 rounded-3xl relative overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-2 right-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                                            width: \"sm\",\n                                                            height: \"sm\",\n                                                            color: \"blue\",\n                                                            className: \"w-4 h-4\",\n                                                            animationPreset: \"gentle\",\n                                                            animationIndex: 2\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 67,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative z-10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-bold mb-2\",\n                                                                children: \"Schedule a call\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 70,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg\",\n                                                                children: \"Book 30 minutes to discuss your project\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 71,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-96 lg:h-[500px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 opacity-40\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftGrid, {\n                                            className: \"w-full h-full text-black\",\n                                            opacity: \"default\",\n                                            animationPreset: \"subtle\",\n                                            animationIndex: 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-16 right-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                            size: \"xl\",\n                                            color: \"red\",\n                                            className: \"w-24 h-24\",\n                                            animationPreset: \"gentle\",\n                                            animationIndex: 3\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-20 left-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedBlob, {\n                                            color: \"blue\",\n                                            className: \"w-32 h-32\",\n                                            animationPreset: \"flowing\",\n                                            animationIndex: 4\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-32 left-16\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedPill, {\n                                            color: \"yellow\",\n                                            className: \"w-20 h-8\",\n                                            animationPreset: \"drift\",\n                                            animationIndex: 5\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-32 right-20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                            width: \"lg\",\n                                            height: \"md\",\n                                            color: \"black\",\n                                            className: \"w-16 h-12\",\n                                            animationPreset: \"gentle\",\n                                            animationIndex: 6\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-8 left-32 opacity-80\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                            size: \"sm\",\n                                            color: \"yellow\",\n                                            className: \"w-6 h-6\",\n                                            animationPreset: \"energetic\",\n                                            animationIndex: 7\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-8 right-8 opacity-70\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedPill, {\n                                            color: \"red\",\n                                            className: \"w-12 h-4\",\n                                            animationPreset: \"float\",\n                                            animationIndex: 8\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-bauhaus-blue text-bauhaus-white overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-5xl font-bold mb-6\",\n                                        children: \"Or send us a message\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl opacity-90\",\n                                        children: \"Tell us about your project and we'll get back to you within 24 hours.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"name\",\n                                                        className: \"block text-sm font-bold uppercase tracking-wide opacity-80\",\n                                                        children: \"Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"name\",\n                                                        name: \"name\",\n                                                        value: formData.name,\n                                                        onChange: handleChange,\n                                                        required: true,\n                                                        className: \"w-full px-6 py-4 bg-bauhaus-white text-bauhaus-black rounded-2xl focus:outline-none focus:ring-2 focus:ring-bauhaus-yellow transition-all duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"email\",\n                                                        className: \"block text-sm font-bold uppercase tracking-wide opacity-80\",\n                                                        children: \"Email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        id: \"email\",\n                                                        name: \"email\",\n                                                        value: formData.email,\n                                                        onChange: handleChange,\n                                                        required: true,\n                                                        className: \"w-full px-6 py-4 bg-bauhaus-white text-bauhaus-black rounded-2xl focus:outline-none focus:ring-2 focus:ring-bauhaus-yellow transition-all duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"message\",\n                                                className: \"block text-sm font-bold uppercase tracking-wide opacity-80\",\n                                                children: \"Message\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                id: \"message\",\n                                                name: \"message\",\n                                                value: formData.message,\n                                                onChange: handleChange,\n                                                required: true,\n                                                rows: 6,\n                                                className: \"w-full px-6 py-4 bg-bauhaus-white text-bauhaus-black rounded-2xl focus:outline-none focus:ring-2 focus:ring-bauhaus-yellow transition-all duration-200 resize-none\",\n                                                placeholder: \"Tell us about your project, timeline, and goals...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center pt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"btn-yellow text-lg px-8 py-4\",\n                                            children: \"Start here\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-16 left-16 opacity-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedBlob, {\n                            color: \"yellow\",\n                            className: \"w-24 h-24\",\n                            animationPreset: \"drift\",\n                            animationIndex: 9\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 right-20 opacity-15\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                            size: \"lg\",\n                            color: \"red\",\n                            className: \"w-20 h-20\",\n                            animationPreset: \"gentle\",\n                            animationIndex: 10\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 right-8 opacity-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                            width: \"md\",\n                            height: \"xl\",\n                            color: \"yellow\",\n                            className: \"w-8 h-24\",\n                            animationPreset: \"float\",\n                            animationIndex: 11\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\contact\\\\page.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/contact/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/shapes/AnimatedShapes */ \"(ssr)/./src/components/shapes/AnimatedShapes.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"relative w-full bg-brand-background text-bauhaus-black overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftGrid, {\n                    className: \"w-full h-full text-black\",\n                    opacity: \"default\",\n                    animationPreset: \"drift\",\n                    animationIndex: 200\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 px-6 md:px-12 lg:px-24 py-16 md:py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-12 gap-12 lg:gap-16 mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-5 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    src: \"/images/logo.png\",\n                                                    alt: \"Navhaus\",\n                                                    width: 140,\n                                                    height: 45,\n                                                    className: \"h-10 w-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-2 -right-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                                        size: \"sm\",\n                                                        color: \"red\",\n                                                        className: \"w-4 h-4\",\n                                                        animationPreset: \"gentle\",\n                                                        animationIndex: 201\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 47,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl font-bold text-bauhaus-black\",\n                                                    children: \"What matters, made real.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 leading-relaxed max-w-sm\",\n                                                    children: \"We build bold, efficient, and meaningful digital experiences. Nothing more, nothing less.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-3 lg:mt-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium\",\n                                                children: \"Home\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/about\",\n                                                className: \"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium\",\n                                                children: \"About\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/work\",\n                                                className: \"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium\",\n                                                children: \"Work\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/contact\",\n                                                className: \"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-4 space-y-8 lg:mt-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-bauhaus-black font-medium\",\n                                                    children: \"Ready to build something?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/contact\",\n                                                    className: \"inline-block px-4 py-2 border-2 border-bauhaus-black bg-transparent text-bauhaus-black font-bold uppercase tracking-wide hover:bg-bauhaus-black hover:text-brand-background transition-colors duration-200 rounded-xl text-sm\",\n                                                    children: \"Start here\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-2 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-bauhaus-red rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 102,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-bauhaus-yellow rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-bauhaus-blue rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Usually responds within 24 hours\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative py-8 mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-px bg-gray-800\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex justify-center space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                            width: \"lg\",\n                                            height: \"sm\",\n                                            color: \"red\",\n                                            className: \"w-16 h-4\",\n                                            animationPreset: \"flowing\",\n                                            animationIndex: 205\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                            size: \"md\",\n                                            color: \"yellow\",\n                                            className: \"w-8 h-8\",\n                                            animationPreset: \"pulse\",\n                                            animationIndex: 206\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedTriangle, {\n                                            size: \"md\",\n                                            color: \"blue\",\n                                            direction: \"up\",\n                                            className: \"w-8 h-8\",\n                                            animationPreset: \"dynamic\",\n                                            animationIndex: 207\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" Navhaus. All rights reserved.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Built with intention\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-bauhaus-red rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-bauhaus-yellow rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-bauhaus-blue rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-16 left-16 opacity-15\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedBlob, {\n                    color: \"red\",\n                    className: \"w-24 h-24\",\n                    animationPreset: \"drift\",\n                    animationIndex: 208\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-20 right-20 opacity-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedQuarterCircle, {\n                    color: \"blue\",\n                    corner: \"top-left\",\n                    className: \"w-32 h-32\",\n                    animationPreset: \"gentle\",\n                    animationIndex: 209\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/2 right-8 opacity-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                    width: \"lg\",\n                    height: \"xl\",\n                    color: \"yellow\",\n                    className: \"w-8 h-24\",\n                    animationPreset: \"float\",\n                    animationIndex: 210\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/4 opacity-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                    size: \"lg\",\n                    color: \"red\",\n                    className: \"w-16 h-16\",\n                    animationPreset: \"energetic\",\n                    animationIndex: 211\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-20 right-1/3 opacity-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedTriangle, {\n                    size: \"lg\",\n                    color: \"yellow\",\n                    direction: \"up\",\n                    className: \"w-12 h-12\",\n                    animationPreset: \"pulse\",\n                    animationIndex: 212\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvRm9vdGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEI7QUFDRjtBQVNlO0FBRTVCLFNBQVNRO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFPQyxXQUFVOzswQkFFaEIsOERBQUNDO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDTCwrRUFBZ0JBO29CQUNmSyxXQUFVO29CQUNWRSxTQUFRO29CQUNSQyxpQkFBZ0I7b0JBQ2hCQyxnQkFBZ0I7Ozs7Ozs7Ozs7OzBCQUtwQiw4REFBQ0g7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNDO29CQUFJRCxXQUFVOztzQ0FHYiw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUdiLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ1YsbURBQUtBO29EQUNKZSxLQUFJO29EQUNKQyxLQUFJO29EQUNKQyxPQUFPO29EQUNQQyxRQUFRO29EQUNSUixXQUFVOzs7Ozs7OERBR1osOERBQUNDO29EQUFJRCxXQUFVOzhEQUNiLDRFQUFDUixpRkFBa0JBO3dEQUNqQmlCLE1BQUs7d0RBQ0xDLE9BQU07d0RBQ05WLFdBQVU7d0RBQ1ZHLGlCQUFnQjt3REFDaEJDLGdCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSXRCLDhEQUFDSDs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNXO29EQUFFWCxXQUFVOzhEQUF1Qzs7Ozs7OzhEQUdwRCw4REFBQ1c7b0RBQUVYLFdBQVU7OERBQXlDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBUTFELDhEQUFDQztvQ0FBSUQsV0FBVTs4Q0FDYiw0RUFBQ1k7d0NBQUlaLFdBQVU7OzBEQUNiLDhEQUFDVCxrREFBSUE7Z0RBQUNzQixNQUFLO2dEQUFJYixXQUFVOzBEQUE2Rjs7Ozs7OzBEQUd0SCw4REFBQ1Qsa0RBQUlBO2dEQUFDc0IsTUFBSztnREFBU2IsV0FBVTswREFBNkY7Ozs7OzswREFHM0gsOERBQUNULGtEQUFJQTtnREFBQ3NCLE1BQUs7Z0RBQVFiLFdBQVU7MERBQTZGOzs7Ozs7MERBRzFILDhEQUFDVCxrREFBSUE7Z0RBQUNzQixNQUFLO2dEQUFXYixXQUFVOzBEQUE2Rjs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBT2pJLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ1c7b0RBQUVYLFdBQVU7OERBQWlDOzs7Ozs7OERBRzlDLDhEQUFDVCxrREFBSUE7b0RBQ0hzQixNQUFLO29EQUNMYixXQUFVOzhEQUNYOzs7Ozs7Ozs7Ozs7c0RBTUgsOERBQUNDOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ0M7b0RBQUlELFdBQVU7O3NFQUNiLDhEQUFDQzs0REFBSUQsV0FBVTs7Ozs7O3NFQUNmLDhEQUFDQzs0REFBSUQsV0FBVTs7Ozs7O3NFQUNmLDhEQUFDQzs0REFBSUQsV0FBVTs7Ozs7Ozs7Ozs7OzhEQUVqQiw4REFBQ2M7OERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNWiw4REFBQ2I7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBSUQsV0FBVTs4Q0FDYiw0RUFBQ0M7d0NBQUlELFdBQVU7Ozs7Ozs7Ozs7OzhDQUVqQiw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDUCx1RkFBd0JBOzRDQUN2QmMsT0FBTTs0Q0FDTkMsUUFBTzs0Q0FDUEUsT0FBTTs0Q0FDTlYsV0FBVTs0Q0FDVkcsaUJBQWdCOzRDQUNoQkMsZ0JBQWdCOzs7Ozs7c0RBRWxCLDhEQUFDWixpRkFBa0JBOzRDQUNqQmlCLE1BQUs7NENBQ0xDLE9BQU07NENBQ05WLFdBQVU7NENBQ1ZHLGlCQUFnQjs0Q0FDaEJDLGdCQUFnQjs7Ozs7O3NEQUVsQiw4REFBQ1YsK0VBQWdCQTs0Q0FDZmUsTUFBSzs0Q0FDTEMsT0FBTTs0Q0FDTkssV0FBVTs0Q0FDVmYsV0FBVTs0Q0FDVkcsaUJBQWdCOzRDQUNoQkMsZ0JBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTXRCLDhEQUFDSDs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFJRCxXQUFVOzt3Q0FBd0I7d0NBQ2xDLElBQUlnQixPQUFPQyxXQUFXO3dDQUFHOzs7Ozs7OzhDQUc5Qiw4REFBQ2hCO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ2M7c0RBQUs7Ozs7OztzREFDTiw4REFBQ2I7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDQztvREFBSUQsV0FBVTs7Ozs7OzhEQUNmLDhEQUFDQztvREFBSUQsV0FBVTs7Ozs7OzhEQUNmLDhEQUFDQztvREFBSUQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUXpCLDhEQUFDQztnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0osMkVBQVlBO29CQUNYYyxPQUFNO29CQUNOVixXQUFVO29CQUNWRyxpQkFBZ0I7b0JBQ2hCQyxnQkFBZ0I7Ozs7Ozs7Ozs7OzBCQUdwQiw4REFBQ0g7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNILG9GQUFxQkE7b0JBQ3BCYSxPQUFNO29CQUNOUSxRQUFPO29CQUNQbEIsV0FBVTtvQkFDVkcsaUJBQWdCO29CQUNoQkMsZ0JBQWdCOzs7Ozs7Ozs7OzswQkFHcEIsOERBQUNIO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDUCx1RkFBd0JBO29CQUN2QmMsT0FBTTtvQkFDTkMsUUFBTztvQkFDUEUsT0FBTTtvQkFDTlYsV0FBVTtvQkFDVkcsaUJBQWdCO29CQUNoQkMsZ0JBQWdCOzs7Ozs7Ozs7OzswQkFHcEIsOERBQUNIO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDUixpRkFBa0JBO29CQUNqQmlCLE1BQUs7b0JBQ0xDLE9BQU07b0JBQ05WLFdBQVU7b0JBQ1ZHLGlCQUFnQjtvQkFDaEJDLGdCQUFnQjs7Ozs7Ozs7Ozs7MEJBR3BCLDhEQUFDSDtnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ04sK0VBQWdCQTtvQkFDZmUsTUFBSztvQkFDTEMsT0FBTTtvQkFDTkssV0FBVTtvQkFDVmYsV0FBVTtvQkFDVkcsaUJBQWdCO29CQUNoQkMsZ0JBQWdCOzs7Ozs7Ozs7Ozs7Ozs7OztBQUsxQiIsInNvdXJjZXMiOlsid2VicGFjazovL25hdmhhdXMtd2Vic2l0ZS8uL3NyYy9jb21wb25lbnRzL2xheW91dC9Gb290ZXIudHN4PzI2MzgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBJbWFnZSBmcm9tICduZXh0L2ltYWdlJ1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IHtcbiAgQW5pbWF0ZWRTb2Z0Q2lyY2xlLFxuICBBbmltYXRlZFJvdW5kZWRSZWN0YW5nbGUsXG4gIEFuaW1hdGVkVHJpYW5nbGUsXG4gIEFuaW1hdGVkU29mdEdyaWQsXG4gIEFuaW1hdGVkQmxvYixcbiAgQW5pbWF0ZWRQaWxsLFxuICBBbmltYXRlZFF1YXJ0ZXJDaXJjbGVcbn0gZnJvbSAnQC9jb21wb25lbnRzL3NoYXBlcy9BbmltYXRlZFNoYXBlcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRm9vdGVyKCkge1xuICByZXR1cm4gKFxuICAgIDxmb290ZXIgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy1mdWxsIGJnLWJyYW5kLWJhY2tncm91bmQgdGV4dC1iYXVoYXVzLWJsYWNrIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgey8qIEJhY2tncm91bmQgR3JpZCBQYXR0ZXJuICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIG9wYWNpdHktMjBcIj5cbiAgICAgICAgPEFuaW1hdGVkU29mdEdyaWRcbiAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIHRleHQtYmxhY2tcIlxuICAgICAgICAgIG9wYWNpdHk9XCJkZWZhdWx0XCJcbiAgICAgICAgICBhbmltYXRpb25QcmVzZXQ9XCJkcmlmdFwiXG4gICAgICAgICAgYW5pbWF0aW9uSW5kZXg9ezIwMH1cbiAgICAgICAgLz5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTWFpbiBGb290ZXIgQ29udGVudCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBweC02IG1kOnB4LTEyIGxnOnB4LTI0IHB5LTE2IG1kOnB5LTIwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG9cIj5cblxuICAgICAgICAgIHsvKiBUb3AgU2VjdGlvbiAtIExvZ28sIE5hdmlnYXRpb24sIGFuZCBDVEEgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0xMiBnYXAtMTIgbGc6Z2FwLTE2IG1iLTE2XCI+XG5cbiAgICAgICAgICAgIHsvKiBMb2dvIGFuZCBUYWdsaW5lICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi01IHNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgICBzcmM9XCIvaW1hZ2VzL2xvZ28ucG5nXCJcbiAgICAgICAgICAgICAgICAgIGFsdD1cIk5hdmhhdXNcIlxuICAgICAgICAgICAgICAgICAgd2lkdGg9ezE0MH1cbiAgICAgICAgICAgICAgICAgIGhlaWdodD17NDV9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTEwIHctYXV0b1wiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICB7LyogRGVjb3JhdGl2ZSBhY2NlbnQgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTIgLXJpZ2h0LThcIj5cbiAgICAgICAgICAgICAgICAgIDxBbmltYXRlZFNvZnRDaXJjbGVcbiAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJyZWRcIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTQgaC00XCJcbiAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uUHJlc2V0PVwiZ2VudGxlXCJcbiAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uSW5kZXg9ezIwMX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtYmF1aGF1cy1ibGFja1wiPlxuICAgICAgICAgICAgICAgICAgV2hhdCBtYXR0ZXJzLCBtYWRlIHJlYWwuXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgbGVhZGluZy1yZWxheGVkIG1heC13LXNtXCI+XG4gICAgICAgICAgICAgICAgICBXZSBidWlsZCBib2xkLCBlZmZpY2llbnQsIGFuZCBtZWFuaW5nZnVsIGRpZ2l0YWwgZXhwZXJpZW5jZXMuXG4gICAgICAgICAgICAgICAgICBOb3RoaW5nIG1vcmUsIG5vdGhpbmcgbGVzcy5cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBOYXZpZ2F0aW9uIExpbmtzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0zIGxnOm10LTE2XCI+XG4gICAgICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9cIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LWJhdWhhdXMtYmxhY2sgaG92ZXI6dGV4dC1iYXVoYXVzLXJlZCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgIEhvbWVcbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hYm91dFwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtYmF1aGF1cy1ibGFjayBob3Zlcjp0ZXh0LWJhdWhhdXMtcmVkIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgQWJvdXRcbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi93b3JrXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1iYXVoYXVzLWJsYWNrIGhvdmVyOnRleHQtYmF1aGF1cy1yZWQgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICBXb3JrXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvY29udGFjdFwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtYmF1aGF1cy1ibGFjayBob3Zlcjp0ZXh0LWJhdWhhdXMtcmVkIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgQ29udGFjdFxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9uYXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIENUQSBTZWN0aW9uICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi00IHNwYWNlLXktOCBsZzptdC0xNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmF1aGF1cy1ibGFjayBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgUmVhZHkgdG8gYnVpbGQgc29tZXRoaW5nP1xuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgaHJlZj1cIi9jb250YWN0XCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1ibG9jayBweC00IHB5LTIgYm9yZGVyLTIgYm9yZGVyLWJhdWhhdXMtYmxhY2sgYmctdHJhbnNwYXJlbnQgdGV4dC1iYXVoYXVzLWJsYWNrIGZvbnQtYm9sZCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZSBob3ZlcjpiZy1iYXVoYXVzLWJsYWNrIGhvdmVyOnRleHQtYnJhbmQtYmFja2dyb3VuZCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDAgcm91bmRlZC14bCB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBTdGFydCBoZXJlXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogUmVzcG9uc2UgVGltZSBJbmRpY2F0b3IgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBnYXAtMiB0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmF1aGF1cy1yZWQgcm91bmRlZC1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmF1aGF1cy15ZWxsb3cgcm91bmRlZC1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmF1aGF1cy1ibHVlIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuPlVzdWFsbHkgcmVzcG9uZHMgd2l0aGluIDI0IGhvdXJzPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIERlY29yYXRpdmUgRGl2aWRlciB3aXRoIEFuaW1hdGVkIEVsZW1lbnRzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgcHktOCBtYi0xMlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtcHggYmctZ3JheS04MDBcIj48L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBmbGV4IGp1c3RpZnktY2VudGVyIHNwYWNlLXgtOFwiPlxuICAgICAgICAgICAgICA8QW5pbWF0ZWRSb3VuZGVkUmVjdGFuZ2xlXG4gICAgICAgICAgICAgICAgd2lkdGg9XCJsZ1wiXG4gICAgICAgICAgICAgICAgaGVpZ2h0PVwic21cIlxuICAgICAgICAgICAgICAgIGNvbG9yPVwicmVkXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTE2IGgtNFwiXG4gICAgICAgICAgICAgICAgYW5pbWF0aW9uUHJlc2V0PVwiZmxvd2luZ1wiXG4gICAgICAgICAgICAgICAgYW5pbWF0aW9uSW5kZXg9ezIwNX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPEFuaW1hdGVkU29mdENpcmNsZVxuICAgICAgICAgICAgICAgIHNpemU9XCJtZFwiXG4gICAgICAgICAgICAgICAgY29sb3I9XCJ5ZWxsb3dcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctOCBoLThcIlxuICAgICAgICAgICAgICAgIGFuaW1hdGlvblByZXNldD1cInB1bHNlXCJcbiAgICAgICAgICAgICAgICBhbmltYXRpb25JbmRleD17MjA2fVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8QW5pbWF0ZWRUcmlhbmdsZVxuICAgICAgICAgICAgICAgIHNpemU9XCJtZFwiXG4gICAgICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcbiAgICAgICAgICAgICAgICBkaXJlY3Rpb249XCJ1cFwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy04IGgtOFwiXG4gICAgICAgICAgICAgICAgYW5pbWF0aW9uUHJlc2V0PVwiZHluYW1pY1wiXG4gICAgICAgICAgICAgICAgYW5pbWF0aW9uSW5kZXg9ezIwN31cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEJvdHRvbSBTZWN0aW9uIC0gQ29weXJpZ2h0IGFuZCBDcmVkaXRzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBtZDpmbGV4LXJvdyBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIHNwYWNlLXktNCBtZDpzcGFjZS15LTBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIMKpIHtuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKCl9IE5hdmhhdXMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTYgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIDxzcGFuPkJ1aWx0IHdpdGggaW50ZW50aW9uPC9zcGFuPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWJhdWhhdXMtcmVkIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1iYXVoYXVzLXllbGxvdyByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmF1aGF1cy1ibHVlIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRmxvYXRpbmcgQmFja2dyb3VuZCBFbGVtZW50cyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTE2IGxlZnQtMTYgb3BhY2l0eS0xNVwiPlxuICAgICAgICA8QW5pbWF0ZWRCbG9iXG4gICAgICAgICAgY29sb3I9XCJyZWRcIlxuICAgICAgICAgIGNsYXNzTmFtZT1cInctMjQgaC0yNFwiXG4gICAgICAgICAgYW5pbWF0aW9uUHJlc2V0PVwiZHJpZnRcIlxuICAgICAgICAgIGFuaW1hdGlvbkluZGV4PXsyMDh9XG4gICAgICAgIC8+XG4gICAgICA8L2Rpdj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTIwIHJpZ2h0LTIwIG9wYWNpdHktMTBcIj5cbiAgICAgICAgPEFuaW1hdGVkUXVhcnRlckNpcmNsZVxuICAgICAgICAgIGNvbG9yPVwiYmx1ZVwiXG4gICAgICAgICAgY29ybmVyPVwidG9wLWxlZnRcIlxuICAgICAgICAgIGNsYXNzTmFtZT1cInctMzIgaC0zMlwiXG4gICAgICAgICAgYW5pbWF0aW9uUHJlc2V0PVwiZ2VudGxlXCJcbiAgICAgICAgICBhbmltYXRpb25JbmRleD17MjA5fVxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0xLzIgcmlnaHQtOCBvcGFjaXR5LThcIj5cbiAgICAgICAgPEFuaW1hdGVkUm91bmRlZFJlY3RhbmdsZVxuICAgICAgICAgIHdpZHRoPVwibGdcIlxuICAgICAgICAgIGhlaWdodD1cInhsXCJcbiAgICAgICAgICBjb2xvcj1cInllbGxvd1wiXG4gICAgICAgICAgY2xhc3NOYW1lPVwidy04IGgtMjRcIlxuICAgICAgICAgIGFuaW1hdGlvblByZXNldD1cImZsb2F0XCJcbiAgICAgICAgICBhbmltYXRpb25JbmRleD17MjEwfVxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS04IGxlZnQtMS80IG9wYWNpdHktMTJcIj5cbiAgICAgICAgPEFuaW1hdGVkU29mdENpcmNsZVxuICAgICAgICAgIHNpemU9XCJsZ1wiXG4gICAgICAgICAgY29sb3I9XCJyZWRcIlxuICAgICAgICAgIGNsYXNzTmFtZT1cInctMTYgaC0xNlwiXG4gICAgICAgICAgYW5pbWF0aW9uUHJlc2V0PVwiZW5lcmdldGljXCJcbiAgICAgICAgICBhbmltYXRpb25JbmRleD17MjExfVxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0yMCByaWdodC0xLzMgb3BhY2l0eS04XCI+XG4gICAgICAgIDxBbmltYXRlZFRyaWFuZ2xlXG4gICAgICAgICAgc2l6ZT1cImxnXCJcbiAgICAgICAgICBjb2xvcj1cInllbGxvd1wiXG4gICAgICAgICAgZGlyZWN0aW9uPVwidXBcIlxuICAgICAgICAgIGNsYXNzTmFtZT1cInctMTIgaC0xMlwiXG4gICAgICAgICAgYW5pbWF0aW9uUHJlc2V0PVwicHVsc2VcIlxuICAgICAgICAgIGFuaW1hdGlvbkluZGV4PXsyMTJ9XG4gICAgICAgIC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Zvb3Rlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkltYWdlIiwiTGluayIsIkFuaW1hdGVkU29mdENpcmNsZSIsIkFuaW1hdGVkUm91bmRlZFJlY3RhbmdsZSIsIkFuaW1hdGVkVHJpYW5nbGUiLCJBbmltYXRlZFNvZnRHcmlkIiwiQW5pbWF0ZWRCbG9iIiwiQW5pbWF0ZWRRdWFydGVyQ2lyY2xlIiwiRm9vdGVyIiwiZm9vdGVyIiwiY2xhc3NOYW1lIiwiZGl2Iiwib3BhY2l0eSIsImFuaW1hdGlvblByZXNldCIsImFuaW1hdGlvbkluZGV4Iiwic3JjIiwiYWx0Iiwid2lkdGgiLCJoZWlnaHQiLCJzaXplIiwiY29sb3IiLCJwIiwibmF2IiwiaHJlZiIsInNwYW4iLCJkaXJlY3Rpb24iLCJEYXRlIiwiZ2V0RnVsbFllYXIiLCJjb3JuZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const navItems = [\n        {\n            href: \"/\",\n            label: \"Home\"\n        },\n        {\n            href: \"/about\",\n            label: \"About\"\n        },\n        // { href: '/work', label: 'Work' }, // Hidden for now\n        {\n            href: \"/contact\",\n            label: \"Contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"w-full py-6 px-6 md:px-12 lg:px-24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                src: \"/images/logo.png\",\n                                alt: \"Navhaus\",\n                                width: 120,\n                                height: 40,\n                                className: \"h-8 w-auto lg:-mt-[15px]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: `font-medium uppercase tracking-wide transition-colors duration-200 ${pathname === item.href ? \"text-bauhaus-red\" : \"text-bauhaus-black hover:text-bauhaus-red\"}`,\n                                        children: item.label\n                                    }, item.href, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 13\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/contact\",\n                                    className: \"btn-primary ml-8\",\n                                    children: \"Start Project\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"md:hidden text-bauhaus-black\",\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden mt-6 py-6 border-t border-bauhaus-black\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `font-medium uppercase tracking-wide transition-colors duration-200 ${pathname === item.href ? \"text-bauhaus-red\" : \"text-bauhaus-black hover:text-bauhaus-red\"}`,\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.label\n                                }, item.href, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/contact\",\n                                className: \"btn-primary inline-block mt-4\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Start Project\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/PageWrapper.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/PageWrapper.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PageWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Footer */ \"(ssr)/./src/components/layout/Footer.tsx\");\n\n\n\nfunction PageWrapper({ children, className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\PageWrapper.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: `flex-1 ${className}`,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\PageWrapper.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\PageWrapper.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\PageWrapper.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvUGFnZVdyYXBwZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE2QjtBQUNBO0FBT2QsU0FBU0UsWUFBWSxFQUFFQyxRQUFRLEVBQUVDLFlBQVksRUFBRSxFQUFvQjtJQUNoRixxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVTs7MEJBQ2IsOERBQUNKLCtDQUFNQTs7Ozs7MEJBQ1AsOERBQUNNO2dCQUFLRixXQUFXLENBQUMsT0FBTyxFQUFFQSxVQUFVLENBQUM7MEJBQ25DRDs7Ozs7OzBCQUVILDhEQUFDRiwrQ0FBTUE7Ozs7Ozs7Ozs7O0FBR2IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uYXZoYXVzLXdlYnNpdGUvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvUGFnZVdyYXBwZXIudHN4PzE2N2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEhlYWRlciBmcm9tICcuL0hlYWRlcidcbmltcG9ydCBGb290ZXIgZnJvbSAnLi9Gb290ZXInXG5cbmludGVyZmFjZSBQYWdlV3JhcHBlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFnZVdyYXBwZXIoeyBjaGlsZHJlbiwgY2xhc3NOYW1lID0gJycgfTogUGFnZVdyYXBwZXJQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggZmxleC1jb2xcIj5cbiAgICAgIDxIZWFkZXIgLz5cbiAgICAgIDxtYWluIGNsYXNzTmFtZT17YGZsZXgtMSAke2NsYXNzTmFtZX1gfT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9tYWluPlxuICAgICAgPEZvb3RlciAvPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiSGVhZGVyIiwiRm9vdGVyIiwiUGFnZVdyYXBwZXIiLCJjaGlsZHJlbiIsImNsYXNzTmFtZSIsImRpdiIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/PageWrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/performance/PerformanceMonitor.tsx":
/*!***********************************************************!*\
  !*** ./src/components/performance/PerformanceMonitor.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PerformanceMonitor)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction PerformanceMonitor() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Only run in development or when explicitly enabled\n        if (false) {}\n        // Monitor LCP\n        const observer = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            const lastEntry = entries[entries.length - 1];\n            if (lastEntry) {\n                console.log(\"\\uD83D\\uDE80 LCP:\", Math.round(lastEntry.startTime), \"ms\");\n                console.log(\"LCP Element:\", lastEntry.element);\n            }\n        });\n        try {\n            observer.observe({\n                entryTypes: [\n                    \"largest-contentful-paint\"\n                ]\n            });\n        } catch (e) {\n            console.log(\"LCP monitoring not supported\");\n        }\n        // Monitor CLS\n        const clsObserver = new PerformanceObserver((list)=>{\n            let clsValue = 0;\n            for (const entry of list.getEntries()){\n                if (!entry.hadRecentInput) {\n                    clsValue += entry.value;\n                }\n            }\n            if (clsValue > 0) {\n                console.log(\"\\uD83D\\uDCD0 CLS:\", clsValue.toFixed(4));\n            }\n        });\n        try {\n            clsObserver.observe({\n                entryTypes: [\n                    \"layout-shift\"\n                ]\n            });\n        } catch (e) {\n            console.log(\"CLS monitoring not supported\");\n        }\n        return ()=>{\n            observer.disconnect();\n            clsObserver.disconnect();\n        };\n    }, []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/performance/PerformanceMonitor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shapes/AnimatedShapes.tsx":
/*!**************************************************!*\
  !*** ./src/components/shapes/AnimatedShapes.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedBlob: () => (/* binding */ AnimatedBlob),\n/* harmony export */   AnimatedCircle: () => (/* binding */ AnimatedCircle),\n/* harmony export */   AnimatedHalfCircle: () => (/* binding */ AnimatedHalfCircle),\n/* harmony export */   AnimatedPill: () => (/* binding */ AnimatedPill),\n/* harmony export */   AnimatedQuarterCircle: () => (/* binding */ AnimatedQuarterCircle),\n/* harmony export */   AnimatedRectangle: () => (/* binding */ AnimatedRectangle),\n/* harmony export */   AnimatedRoundedRect: () => (/* binding */ AnimatedRoundedRect),\n/* harmony export */   AnimatedRoundedRectangle: () => (/* binding */ AnimatedRoundedRectangle),\n/* harmony export */   AnimatedSoftCircle: () => (/* binding */ AnimatedSoftCircle),\n/* harmony export */   AnimatedSoftGrid: () => (/* binding */ AnimatedSoftGrid),\n/* harmony export */   AnimatedTriangle: () => (/* binding */ AnimatedTriangle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useScrollAnimation */ \"(ssr)/./src/hooks/useScrollAnimation.ts\");\n/* harmony import */ var _Circle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Circle */ \"(ssr)/./src/components/shapes/Circle.tsx\");\n/* harmony import */ var _Rectangle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Rectangle */ \"(ssr)/./src/components/shapes/Rectangle.tsx\");\n/* harmony import */ var _Triangle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Triangle */ \"(ssr)/./src/components/shapes/Triangle.tsx\");\n/* harmony import */ var _HalfCircle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./HalfCircle */ \"(ssr)/./src/components/shapes/HalfCircle.tsx\");\n/* harmony import */ var _RoundedShapes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./RoundedShapes */ \"(ssr)/./src/components/shapes/RoundedShapes.tsx\");\n/* __next_internal_client_entry_do_not_use__ AnimatedCircle,AnimatedSoftCircle,AnimatedRectangle,AnimatedRoundedRectangle,AnimatedTriangle,AnimatedHalfCircle,AnimatedSoftGrid,AnimatedRoundedRect,AnimatedPill,AnimatedBlob,AnimatedQuarterCircle auto */ \n\n\n\n\n\n\n// Generate a consistent random seed based on position and type\nconst generateSeed = (type, index = 0)=>{\n    let hash = 0;\n    const str = `${type}-${index}`;\n    for(let i = 0; i < str.length; i++){\n        const char = str.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash // Convert to 32-bit integer\n        ;\n    }\n    return Math.abs(hash) / 2147483647 // Normalize to 0-1\n    ;\n};\nfunction AnimatedCircle({ animationPreset = \"gentle\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"circle\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Circle__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedSoftCircle({ animationPreset = \"subtle\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"soft-circle\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Circle__WEBPACK_IMPORTED_MODULE_2__.SoftCircle, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedRectangle({ animationPreset = \"gentle\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"rectangle\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Rectangle__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedRoundedRectangle({ animationPreset = \"gentle\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"rounded-rectangle\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Rectangle__WEBPACK_IMPORTED_MODULE_3__.RoundedRectangle, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedTriangle({ animationPreset = \"dynamic\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"triangle\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Triangle__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedHalfCircle({ animationPreset = \"gentle\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"half-circle\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HalfCircle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedSoftGrid({ animationPreset = \"subtle\", animationIndex = 0, opacity = \"default\", ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"soft-grid\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RoundedShapes__WEBPACK_IMPORTED_MODULE_6__.SoftGrid, {\n            opacity: opacity,\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedRoundedRect({ animationPreset = \"gentle\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"rounded-rect\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RoundedShapes__WEBPACK_IMPORTED_MODULE_6__.RoundedRect, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 204,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedPill({ animationPreset = \"horizontal\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"pill\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RoundedShapes__WEBPACK_IMPORTED_MODULE_6__.Pill, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedBlob({ animationPreset = \"dynamic\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"blob\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RoundedShapes__WEBPACK_IMPORTED_MODULE_6__.Blob, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedQuarterCircle({ animationPreset = \"gentle\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"quarter-circle\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RoundedShapes__WEBPACK_IMPORTED_MODULE_6__.QuarterCircle, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 260,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shapes/AnimatedShapes.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shapes/Circle.tsx":
/*!******************************************!*\
  !*** ./src/components/shapes/Circle.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SoftCircle: () => (/* binding */ SoftCircle),\n/* harmony export */   \"default\": () => (/* binding */ Circle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst sizeClasses = {\n    sm: \"w-8 h-8\",\n    md: \"w-16 h-16\",\n    lg: \"w-24 h-24\",\n    xl: \"w-32 h-32\"\n};\nconst colorClasses = {\n    red: \"bg-brand-red\",\n    yellow: \"bg-brand-yellow\",\n    blue: \"bg-brand-blue\",\n    black: \"bg-black\",\n    white: \"bg-white border border-black\"\n};\nfunction Circle({ size = \"md\", color = \"red\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-full ${sizeClasses[size]} ${colorClasses[color]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Circle.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n// Soft Circle with organic feel (inspired by logo roundedness)\nfunction SoftCircle({ size = \"md\", color = \"red\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-full ${sizeClasses[size]} ${colorClasses[color]} ${className}`,\n        style: {\n            filter: \"blur(0.5px)\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Circle.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shapes/Circle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shapes/HalfCircle.tsx":
/*!**********************************************!*\
  !*** ./src/components/shapes/HalfCircle.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HalfCircle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst sizeClasses = {\n    sm: \"w-8 h-4\",\n    md: \"w-16 h-8\",\n    lg: \"w-24 h-12\",\n    xl: \"w-32 h-16\"\n};\nconst colorClasses = {\n    red: \"bg-bauhaus-red\",\n    yellow: \"bg-bauhaus-yellow\",\n    blue: \"bg-bauhaus-blue\",\n    black: \"bg-bauhaus-black\",\n    white: \"bg-bauhaus-white border border-bauhaus-black\"\n};\nconst directionClasses = {\n    top: \"rounded-t-full\",\n    bottom: \"rounded-b-full\",\n    left: \"rounded-l-full\",\n    right: \"rounded-r-full\"\n};\nconst getRotationClasses = (direction)=>{\n    switch(direction){\n        case \"left\":\n            return \"w-4 h-8 md:w-8 md:h-16 lg:w-12 lg:h-24 xl:w-16 xl:h-32\";\n        case \"right\":\n            return \"w-4 h-8 md:w-8 md:h-16 lg:w-12 lg:h-24 xl:w-16 xl:h-32\";\n        default:\n            return \"\";\n    }\n};\nfunction HalfCircle({ size = \"md\", color = \"red\", direction = \"top\", className = \"\" }) {\n    const rotationClasses = getRotationClasses(direction);\n    const finalSizeClasses = rotationClasses || sizeClasses[size];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${finalSizeClasses} ${colorClasses[color]} ${directionClasses[direction]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\HalfCircle.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shapes/HalfCircle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shapes/Rectangle.tsx":
/*!*********************************************!*\
  !*** ./src/components/shapes/Rectangle.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PillRectangle: () => (/* binding */ PillRectangle),\n/* harmony export */   RoundedRectangle: () => (/* binding */ RoundedRectangle),\n/* harmony export */   \"default\": () => (/* binding */ Rectangle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst widthClasses = {\n    sm: \"w-12\",\n    md: \"w-24\",\n    lg: \"w-32\",\n    xl: \"w-48\"\n};\nconst heightClasses = {\n    sm: \"h-8\",\n    md: \"h-16\",\n    lg: \"h-24\",\n    xl: \"h-32\"\n};\nconst colorClasses = {\n    red: \"bg-brand-red\",\n    yellow: \"bg-brand-yellow\",\n    blue: \"bg-brand-blue\",\n    black: \"bg-black\",\n    white: \"bg-white border border-black\"\n};\nfunction Rectangle({ width = \"md\", height = \"md\", color = \"blue\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${widthClasses[width]} ${heightClasses[height]} ${colorClasses[color]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Rectangle.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n// Rounded Rectangle (inspired by logo's soft corners)\nfunction RoundedRectangle({ width = \"md\", height = \"md\", color = \"blue\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-3xl ${widthClasses[width]} ${heightClasses[height]} ${colorClasses[color]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Rectangle.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n// Pill shape (very rounded rectangle)\nfunction PillRectangle({ width = \"md\", height = \"md\", color = \"yellow\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-full ${widthClasses[width]} ${heightClasses[height]} ${colorClasses[color]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Rectangle.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shapes/Rectangle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shapes/RoundedShapes.tsx":
/*!*************************************************!*\
  !*** ./src/components/shapes/RoundedShapes.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* binding */ Blob),\n/* harmony export */   HalfCircle: () => (/* binding */ HalfCircle),\n/* harmony export */   Pill: () => (/* binding */ Pill),\n/* harmony export */   QuarterCircle: () => (/* binding */ QuarterCircle),\n/* harmony export */   RoundedRect: () => (/* binding */ RoundedRect),\n/* harmony export */   RoundedSquareWithCutout: () => (/* binding */ RoundedSquareWithCutout),\n/* harmony export */   SoftGrid: () => (/* binding */ SoftGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\n// Rounded Rectangle with soft corners\nfunction RoundedRect({ className = \"\", color = \"blue\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${colorClasses[color]} rounded-3xl ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n// Pill shape (very rounded rectangle)\nfunction Pill({ className = \"\", color = \"yellow\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${colorClasses[color]} rounded-full ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n// Organic blob shape using CSS\nfunction Blob({ className = \"\", color = \"red\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${colorClasses[color]} ${className}`,\n        style: {\n            borderRadius: \"60% 40% 30% 70% / 60% 30% 70% 40%\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n// Half circle (like in your references)\nfunction HalfCircle({ className = \"\", color = \"blue\", direction = \"right\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    const directionClasses = {\n        right: \"rounded-l-full\",\n        left: \"rounded-r-full\",\n        top: \"rounded-b-full\",\n        bottom: \"rounded-t-full\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${colorClasses[color]} ${directionClasses[direction]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n// Quarter circle\nfunction QuarterCircle({ className = \"\", color = \"yellow\", corner = \"top-left\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    const cornerClasses = {\n        \"top-left\": \"rounded-br-full\",\n        \"top-right\": \"rounded-bl-full\",\n        \"bottom-left\": \"rounded-tr-full\",\n        \"bottom-right\": \"rounded-tl-full\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${colorClasses[color]} ${cornerClasses[corner]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n// Rounded square with cutout (inspired by your logo)\nfunction RoundedSquareWithCutout({ className = \"\", color = \"black\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${colorClasses[color]} rounded-3xl w-full h-full`\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-brand-background rounded-full w-1/2 h-1/2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n// Soft grid overlay (like in the DSM reference)\nfunction SoftGrid({ className = \"\", opacity = \"default\" }) {\n    const opacityClass = opacity === \"hero\" ? \"opacity-40\" : \"opacity-20\";\n    const strokeOpacity = opacity === \"hero\" ? \"0.5\" : \"0.3\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `absolute inset-0 ${opacityClass} ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            width: \"100%\",\n            height: \"100%\",\n            className: \"w-full h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pattern\", {\n                        id: \"grid\",\n                        width: \"40\",\n                        height: \"40\",\n                        patternUnits: \"userSpaceOnUse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M 40 0 L 0 0 0 40\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"1\",\n                            opacity: strokeOpacity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    width: \"100%\",\n                    height: \"100%\",\n                    fill: \"url(#grid)\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shapes/RoundedShapes.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shapes/Triangle.tsx":
/*!********************************************!*\
  !*** ./src/components/shapes/Triangle.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Triangle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst sizeClasses = {\n    sm: \"w-0 h-0 border-l-[16px] border-r-[16px] border-b-[28px]\",\n    md: \"w-0 h-0 border-l-[24px] border-r-[24px] border-b-[42px]\",\n    lg: \"w-0 h-0 border-l-[32px] border-r-[32px] border-b-[56px]\",\n    xl: \"w-0 h-0 border-l-[48px] border-r-[48px] border-b-[84px]\"\n};\nconst getTriangleClasses = (size, color, direction)=>{\n    const colorMap = {\n        red: \"#e94436\",\n        yellow: \"#ffc527\",\n        blue: \"#434897\",\n        black: \"#000000\",\n        white: \"#ffffff\"\n    };\n    const baseSize = sizeClasses[size];\n    const triangleColor = colorMap[color];\n    switch(direction){\n        case \"up\":\n            return `${baseSize} border-l-transparent border-r-transparent`;\n        case \"down\":\n            return baseSize.replace(\"border-b-\", \"border-t-\") + \" border-l-transparent border-r-transparent\";\n        case \"left\":\n            return baseSize.replace(\"border-l-\", \"border-r-\").replace(\"border-r-\", \"border-t-\").replace(\"border-b-\", \"border-l-\") + \" border-t-transparent border-b-transparent\";\n        case \"right\":\n            return baseSize.replace(\"border-r-\", \"border-l-\").replace(\"border-l-\", \"border-t-\").replace(\"border-b-\", \"border-r-\") + \" border-t-transparent border-b-transparent\";\n        default:\n            return `${baseSize} border-l-transparent border-r-transparent`;\n    }\n};\nfunction Triangle({ size = \"md\", color = \"yellow\", direction = \"up\", className = \"\" }) {\n    const triangleClasses = getTriangleClasses(size, color, direction);\n    const style = {\n        borderBottomColor: direction === \"up\" ? color === \"red\" ? \"#e94436\" : color === \"yellow\" ? \"#ffc527\" : color === \"blue\" ? \"#434897\" : color === \"black\" ? \"#000000\" : \"#ffffff\" : \"transparent\",\n        borderTopColor: direction === \"down\" ? color === \"red\" ? \"#e94436\" : color === \"yellow\" ? \"#ffc527\" : color === \"blue\" ? \"#434897\" : color === \"black\" ? \"#000000\" : \"#ffffff\" : \"transparent\",\n        borderLeftColor: direction === \"right\" ? color === \"red\" ? \"#e94436\" : color === \"yellow\" ? \"#ffc527\" : color === \"blue\" ? \"#434897\" : color === \"black\" ? \"#000000\" : \"#ffffff\" : \"transparent\",\n        borderRightColor: direction === \"left\" ? color === \"red\" ? \"#e94436\" : color === \"yellow\" ? \"#ffc527\" : color === \"blue\" ? \"#434897\" : color === \"black\" ? \"#000000\" : \"#ffffff\" : \"transparent\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${triangleClasses} ${className}`,\n        style: style\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Triangle.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shapes/Triangle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useScrollAnimation.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useScrollAnimation.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animationPresets: () => (/* binding */ animationPresets),\n/* harmony export */   useScrollAnimation: () => (/* binding */ useScrollAnimation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useScrollAnimation,animationPresets auto */ \nfunction useScrollAnimation(config) {\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [randomFactors, setRandomFactors] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Generate consistent random factors based on seed with more varied directions\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Defer initialization to avoid blocking initial render\n        const timer = setTimeout(()=>{\n            const seed = config.randomSeed || Math.random();\n            // Use multiple different seeds to create more varied movement patterns\n            const xSeed = seed * 12.9898;\n            const ySeed = seed * 78.233;\n            const angleSeed = seed * 45.164;\n            // Generate base random values\n            const baseX = Math.sin(xSeed) * 43758.5453 % 1;\n            const baseY = Math.sin(ySeed) * 43758.5453 % 1;\n            const angle = Math.sin(angleSeed) * 43758.5453 % 1;\n            // Create more dynamic directional patterns\n            // Use angle to create diagonal movements and varied directions\n            const angleRad = angle * Math.PI * 2 // Full circle\n            ;\n            const magnitude = 0.8 + Math.abs(baseX) * 0.4 // Vary magnitude between 0.8-1.2\n            ;\n            setRandomFactors({\n                x: Math.cos(angleRad) * magnitude,\n                y: Math.sin(angleRad) * magnitude\n            });\n            setIsInitialized(true);\n        }, 50) // Small delay to prioritize LCP\n        ;\n        return ()=>clearTimeout(timer);\n    }, [\n        config.randomSeed\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!isInitialized) return;\n        const handleScroll = ()=>{\n            setScrollY(window.scrollY);\n        };\n        // Use passive listener for better performance\n        window.addEventListener(\"scroll\", handleScroll, {\n            passive: true\n        });\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, [\n        isInitialized\n    ]);\n    // Calculate transform values\n    const getTransform = ()=>{\n        if (!elementRef.current || !isInitialized) return \"translate3d(0, 0, 0)\";\n        const rect = elementRef.current.getBoundingClientRect();\n        const elementTop = rect.top + scrollY;\n        const scrollProgress = (scrollY - elementTop + window.innerHeight) / (window.innerHeight + rect.height);\n        // Smooth easing function\n        const easeInOutQuad = (t)=>{\n            return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;\n        };\n        const easedProgress = easeInOutQuad(Math.max(0, Math.min(1, scrollProgress)));\n        let translateX = 0;\n        let translateY = 0;\n        if (config.direction === \"x\" || config.direction === \"both\") {\n            translateX = easedProgress * config.intensity * randomFactors.x * config.speed;\n        }\n        if (config.direction === \"y\" || config.direction === \"both\") {\n            translateY = easedProgress * config.intensity * randomFactors.y * config.speed;\n        }\n        return `translate3d(${translateX}px, ${translateY}px, 0)`;\n    };\n    return {\n        ref: elementRef,\n        style: {\n            transform: getTransform(),\n            transition: \"transform 0.1s ease-out\",\n            willChange: \"transform\"\n        }\n    };\n}\n// Predefined animation presets for different shape types with varied movement\nconst animationPresets = {\n    subtle: {\n        direction: \"both\",\n        intensity: 20,\n        speed: 0.6\n    },\n    gentle: {\n        direction: \"both\",\n        intensity: 30,\n        speed: 0.8\n    },\n    dynamic: {\n        direction: \"both\",\n        intensity: 45,\n        speed: 1.2\n    },\n    flowing: {\n        direction: \"both\",\n        intensity: 35,\n        speed: 0.9\n    },\n    energetic: {\n        direction: \"both\",\n        intensity: 50,\n        speed: 1.4\n    },\n    drift: {\n        direction: \"both\",\n        intensity: 25,\n        speed: 0.5\n    },\n    pulse: {\n        direction: \"both\",\n        intensity: 35,\n        speed: 1.1\n    },\n    float: {\n        direction: \"both\",\n        intensity: 15,\n        speed: 0.4\n    },\n    horizontal: {\n        direction: \"x\",\n        intensity: 30,\n        speed: 0.8\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useScrollAnimation.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"acb4a596c62b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmF2aGF1cy13ZWJzaXRlLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9lOTdlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYWNiNGE1OTZjNjJiXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/contact/page.tsx":
/*!**********************************!*\
  !*** ./src/app/contact/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\app\contact\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Barlow_arguments_subsets_latin_weight_300_400_500_600_700_800_900_display_swap_preload_true_variable_font_barlow_variableName_barlow___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Barlow\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"display\":\"swap\",\"preload\":true,\"variable\":\"--font-barlow\"}],\"variableName\":\"barlow\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Barlow\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"display\\\":\\\"swap\\\",\\\"preload\\\":true,\\\"variable\\\":\\\"--font-barlow\\\"}],\\\"variableName\\\":\\\"barlow\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Barlow_arguments_subsets_latin_weight_300_400_500_600_700_800_900_display_swap_preload_true_variable_font_barlow_variableName_barlow___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Barlow_arguments_subsets_latin_weight_300_400_500_600_700_800_900_display_swap_preload_true_variable_font_barlow_variableName_barlow___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_performance_PerformanceMonitor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/performance/PerformanceMonitor */ \"(rsc)/./src/components/performance/PerformanceMonitor.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"navhaus | what matters, made real\",\n    description: \"Navhaus is a design and development studio that builds bold, efficient, and meaningful digital experiences - nothing more, nothing less.\",\n    icons: {\n        icon: \"/images/icon.png\",\n        shortcut: \"/images/icon.png\",\n        apple: \"/images/icon.png\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Barlow_arguments_subsets_latin_weight_300_400_500_600_700_800_900_display_swap_preload_true_variable_font_barlow_variableName_barlow___WEBPACK_IMPORTED_MODULE_3___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"dns-prefetch\",\n                    href: \"https://fonts.googleapis.com\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_src_app_layout_tsx_import_Barlow_arguments_subsets_latin_weight_300_400_500_600_700_800_900_display_swap_preload_true_variable_font_barlow_variableName_barlow___WEBPACK_IMPORTED_MODULE_3___default().className),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_performance_PerformanceMonitor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    children\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/performance/PerformanceMonitor.tsx":
/*!***********************************************************!*\
  !*** ./src/components/performance/PerformanceMonitor.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\performance\PerformanceMonitor.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();