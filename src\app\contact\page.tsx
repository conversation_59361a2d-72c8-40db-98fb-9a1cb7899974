'use client'

import { useState } from 'react'
import PageWrapper from '@/components/layout/PageWrapper'
import {
  AnimatedSoftCircle,
  AnimatedRoundedRectangle,
  AnimatedSoftGrid,
  AnimatedBlob,
  AnimatedPill
} from '@/components/shapes/AnimatedShapes'

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log('Form submitted:', formData)
    // Reset form
    setFormData({ name: '', email: '', message: '' })
  }

  return (
    <PageWrapper>
      {/* Hero Section */}
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {/* Content Section */}
            <div className="space-y-8">
              <div className="space-y-6">
                <h1 className="text-5xl md:text-6xl font-bold leading-none">
                  Have something worth building?
                </h1>
                <p className="text-xl leading-relaxed text-gray-700 max-w-lg">
                  We like projects that are sharp, fast, and meaningful.
                  If that's what you've got, we should talk.
                </p>
              </div>

              {/* Contact Cards */}
              <div className="space-y-4">
                <div className="bg-bauhaus-blue text-bauhaus-white p-6 rounded-3xl relative overflow-hidden">
                  <div className="absolute bottom-2 right-2">
                    <AnimatedSoftCircle size="sm" color="yellow" className="w-4 h-4" animationPreset="gentle" animationIndex={1} />
                  </div>
                  <div className="relative z-10">
                    <h3 className="font-bold mb-2">Email us directly</h3>
                    <p className="text-lg"><EMAIL></p>
                  </div>
                </div>

                <div className="bg-bauhaus-yellow text-bauhaus-black p-6 rounded-3xl relative overflow-hidden">
                  <div className="absolute top-2 right-2">
                    <AnimatedRoundedRectangle width="sm" height="sm" color="blue" className="w-4 h-4" animationPreset="gentle" animationIndex={2} />
                  </div>
                  <div className="relative z-10">
                    <h3 className="font-bold mb-2">Schedule a call</h3>
                    <p className="text-lg">Book 30 minutes to discuss your project</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Visual Composition */}
            <div className="relative h-96 lg:h-[500px]">
              {/* Background grid */}
              <div className="absolute inset-0 opacity-40">
                <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="subtle" animationIndex={0} />
              </div>

              {/* Main focal elements */}
              <div className="absolute top-16 right-12">
                <AnimatedSoftCircle size="xl" color="red" className="w-24 h-24" animationPreset="gentle" animationIndex={3} />
              </div>

              <div className="absolute bottom-20 left-8">
                <AnimatedBlob color="blue" className="w-32 h-32" animationPreset="flowing" animationIndex={4} />
              </div>

              <div className="absolute top-32 left-16">
                <AnimatedPill color="yellow" className="w-20 h-8" animationPreset="drift" animationIndex={5} />
              </div>

              <div className="absolute bottom-32 right-20">
                <AnimatedRoundedRectangle width="lg" height="md" color="black" className="w-16 h-12" animationPreset="gentle" animationIndex={6} />
              </div>

              {/* Smaller decorative elements */}
              <div className="absolute top-8 left-32 opacity-80">
                <AnimatedSoftCircle size="sm" color="yellow" className="w-6 h-6" animationPreset="energetic" animationIndex={7} />
              </div>

              <div className="absolute bottom-8 right-8 opacity-70">
                <AnimatedPill color="red" className="w-12 h-4" animationPreset="float" animationIndex={8} />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-bauhaus-blue text-bauhaus-white overflow-hidden">
        <div className="max-w-4xl mx-auto relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Or send us a message
            </h2>
            <p className="text-xl opacity-90">
              Tell us about your project and we'll get back to you within 24 hours.
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label htmlFor="name" className="block text-sm font-bold uppercase tracking-wide opacity-80">
                  Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-6 py-4 bg-bauhaus-white text-bauhaus-black rounded-2xl focus:outline-none focus:ring-2 focus:ring-bauhaus-yellow transition-all duration-200"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-bold uppercase tracking-wide opacity-80">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full px-6 py-4 bg-bauhaus-white text-bauhaus-black rounded-2xl focus:outline-none focus:ring-2 focus:ring-bauhaus-yellow transition-all duration-200"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="message" className="block text-sm font-bold uppercase tracking-wide opacity-80">
                Message
              </label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                required
                rows={6}
                className="w-full px-6 py-4 bg-bauhaus-white text-bauhaus-black rounded-2xl focus:outline-none focus:ring-2 focus:ring-bauhaus-yellow transition-all duration-200 resize-none"
                placeholder="Tell us about your project, timeline, and goals..."
              />
            </div>

            <div className="text-center pt-4">
              <button type="submit" className="btn-yellow text-lg px-8 py-4">
                Start here
              </button>
            </div>
          </form>
        </div>

        {/* Background decorative elements */}
        <div className="absolute top-16 left-16 opacity-20">
          <AnimatedBlob color="yellow" className="w-24 h-24" animationPreset="drift" animationIndex={9} />
        </div>
        <div className="absolute bottom-20 right-20 opacity-15">
          <AnimatedSoftCircle size="lg" color="red" className="w-20 h-20" animationPreset="gentle" animationIndex={10} />
        </div>
        <div className="absolute top-1/2 right-8 opacity-10">
          <AnimatedRoundedRectangle width="md" height="xl" color="yellow" className="w-8 h-24" animationPreset="float" animationIndex={11} />
        </div>
      </section>
    </PageWrapper>
  )
}
